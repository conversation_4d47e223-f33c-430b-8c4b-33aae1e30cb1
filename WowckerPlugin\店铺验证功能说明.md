# WhatsApp 群发店铺验证功能实现说明

## 功能概述

在 WhatsApp 登录成功后，系统会自动检查 `stores` 表中是否存在店铺记录。如果没有店铺记录，系统将阻止用户访问群发功能，并显示提示弹窗要求用户先创建店铺。

## 实现位置

**文件**: `WowckerPlugin/PyOneDark_Qt_Widgets_Modern_GUI-master/gui/uis/pages/page_bulk_messaging.py`

## 核心功能

### 1. 店铺验证检查

在 `check_whatsapp_status()` 方法中，当检测到 WhatsApp 状态为 `ready` 时：

```python
if node_status == 'ready':
    # 已完全登录并准备就绪，但需要先验证店铺
    if self._validate_store_exists():
        # 店铺验证通过，允许使用群发功能
        self.is_whatsapp_logged_in = True
        self.login_status_changed.emit(True, "状态：已登录")
        # ... 其他登录成功逻辑
    else:
        # 店铺验证失败，阻止使用群发功能
        self.is_whatsapp_logged_in = False
        self.login_status_changed.emit(False, "状态：需要创建店铺")
        # 显示店铺验证失败的弹窗
        self._show_store_validation_popup()
```

### 2. 店铺验证方法

```python
def _validate_store_exists(self) -> bool:
    """验证stores表中是否存在店铺记录"""
    try:
        from database.store_operations import StoreOperations
        from database.db_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        store_ops = StoreOperations(db_manager)
        stores = store_ops.get_all_stores()
        
        if stores and len(stores) > 0:
            logger.info(f"店铺验证通过，找到 {len(stores)} 个店铺")
            return True
        else:
            logger.warning("店铺验证失败，stores表为空")
            return False
    except Exception as e:
        logger.error(f"验证店铺时出错: {str(e)}")
        return False
```

### 3. 弹窗显示方法

```python
def _show_store_validation_popup(self):
    """显示店铺验证失败的弹窗"""
    msg_box = QMessageBox(self.ui_pages.page_bulk_messaging)
    msg_box.setWindowTitle("需要创建店铺")
    msg_box.setText("请在官网上创建店铺")
    msg_box.setInformativeText("您需要先在官网上创建店铺才能使用WhatsApp群发功能。")
    msg_box.setIcon(QMessageBox.Warning)
    msg_box.setStandardButtons(QMessageBox.Ok)
    # ... 样式设置和显示
```

## 功能流程

1. **WhatsApp 登录成功**: 当 `node_status` 变为 `ready` 时
2. **店铺验证**: 调用 `_validate_store_exists()` 检查 stores 表
3. **验证通过**: 如果有店铺记录
   - 设置 `is_whatsapp_logged_in = True`
   - 显示 "状态：已登录"
   - 启用群发功能
4. **验证失败**: 如果 stores 表为空
   - 设置 `is_whatsapp_logged_in = False`
   - 显示 "状态：需要创建店铺"
   - 显示弹窗提示用户创建店铺
   - 禁用群发功能

## 用户体验

### 有店铺的情况
- ✅ WhatsApp 登录成功
- ✅ 状态显示："状态：已登录"
- ✅ 可以正常使用群发功能

### 无店铺的情况
- ❌ WhatsApp 虽然登录成功，但功能被阻止
- ⚠️ 状态显示："状态：需要创建店铺"
- 📋 弹窗提示："请在官网上创建店铺"
- 🚫 群发功能被禁用

## 弹窗样式

弹窗采用深色主题，与应用整体风格保持一致：
- 背景色：`#2c313c`
- 文字颜色：`#ffffff`
- 按钮颜色：`#568af2`
- 悬停效果：`#4a7bc8`

## 测试验证

提供了两个测试脚本：

1. **test_store_validation.py**: 测试当前数据库的店铺验证状态
2. **test_empty_store_validation.py**: 模拟空店铺表的验证流程

## 技术特点

- **实时验证**: 每次 WhatsApp 状态检查时都会验证店铺
- **用户友好**: 清晰的错误提示和引导
- **安全性**: 确保只有有店铺的用户才能使用群发功能
- **一致性**: 与现有的积分系统检查保持一致
- **可维护性**: 代码结构清晰，易于维护和扩展

## 注意事项

1. 验证在每次状态检查时都会执行，确保实时性
2. 弹窗只在验证失败时显示一次，避免重复打扰
3. 验证失败时会记录日志，便于调试
4. 异常处理完善，确保系统稳定性
