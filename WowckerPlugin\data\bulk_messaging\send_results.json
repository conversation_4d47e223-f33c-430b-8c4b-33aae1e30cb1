[{"number": "<EMAIL>", "status": "failed", "error": "Protocol error (Runtime.callFunctionOn): Session closed. Most likely the page has been closed.", "timestamp": 1750525278207}, {"number": "<EMAIL>", "status": "failed", "error": "Protocol error (Runtime.callFunctionOn): Session closed. Most likely the page has been closed.", "timestamp": 1750525278207}, {"number": "<EMAIL>", "status": "failed", "error": "Protocol error (Runtime.callFunctionOn): Session closed. Most likely the page has been closed.", "timestamp": 1750525278207}, {"number": "<EMAIL>", "status": "failed", "error": "Protocol error (Runtime.callFunctionOn): Session closed. Most likely the page has been closed.", "timestamp": 1750525278208}, {"number": "<EMAIL>", "status": "failed", "error": "Protocol error (Runtime.callFunctionOn): Session closed. Most likely the page has been closed.", "timestamp": 1750525278208}, {"number": "<EMAIL>", "status": "failed", "error": "Protocol error (Runtime.callFunctionOn): Session closed. Most likely the page has been closed.", "timestamp": 1750525278208}, {"number": "<EMAIL>", "status": "failed", "error": "Protocol error (Runtime.callFunctionOn): Session closed. Most likely the page has been closed.", "timestamp": 1750525278208}]