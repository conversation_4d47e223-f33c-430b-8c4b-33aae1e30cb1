[{"number": "<EMAIL>", "status": "failed", "error": "Protocol error (Runtime.callFunctionOn): Session closed. Most likely the page has been closed.", "timestamp": 1750525604050}, {"number": "<EMAIL>", "status": "failed", "error": "Protocol error (Runtime.callFunctionOn): Session closed. Most likely the page has been closed.", "timestamp": 1750525604050}, {"number": "<EMAIL>", "status": "failed", "error": "Protocol error (Runtime.callFunctionOn): Session closed. Most likely the page has been closed.", "timestamp": 1750525604051}, {"number": "<EMAIL>", "status": "failed", "error": "Protocol error (Runtime.callFunctionOn): Session closed. Most likely the page has been closed.", "timestamp": 1750525604051}, {"number": "<EMAIL>", "status": "failed", "error": "Protocol error (Runtime.callFunctionOn): Session closed. Most likely the page has been closed.", "timestamp": 1750525604051}, {"number": "<EMAIL>", "status": "failed", "error": "Protocol error (Runtime.callFunctionOn): Session closed. Most likely the page has been closed.", "timestamp": 1750525604051}, {"number": "<EMAIL>", "status": "failed", "error": "Protocol error (Runtime.callFunctionOn): Session closed. Most likely the page has been closed.", "timestamp": 1750525604051}]