[{"number": "<EMAIL>", "status": "failed", "error": "Protocol error (Runtime.callFunctionOn): Session closed. Most likely the page has been closed.", "timestamp": 1750524796641}, {"number": "<EMAIL>", "status": "failed", "error": "Protocol error (Runtime.callFunctionOn): Session closed. Most likely the page has been closed.", "timestamp": 1750524796641}, {"number": "<EMAIL>", "status": "failed", "error": "Protocol error (Runtime.callFunctionOn): Session closed. Most likely the page has been closed.", "timestamp": 1750524796641}, {"number": "<EMAIL>", "status": "failed", "error": "Protocol error (Runtime.callFunctionOn): Session closed. Most likely the page has been closed.", "timestamp": 1750524796641}, {"number": "<EMAIL>", "status": "failed", "error": "Protocol error (Runtime.callFunctionOn): Session closed. Most likely the page has been closed.", "timestamp": 1750524796641}, {"number": "<EMAIL>", "status": "failed", "error": "Protocol error (Runtime.callFunctionOn): Session closed. Most likely the page has been closed.", "timestamp": 1750524796641}, {"number": "<EMAIL>", "status": "failed", "error": "Protocol error (Runtime.callFunctionOn): Session closed. Most likely the page has been closed.", "timestamp": 1750524796642}]