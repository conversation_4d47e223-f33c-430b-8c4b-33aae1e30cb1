[{"number": "<EMAIL>", "status": "failed", "error": "Protocol error (Runtime.callFunctionOn): Session closed. Most likely the page has been closed.", "timestamp": 1750500663765}, {"number": "<EMAIL>", "status": "failed", "error": "Protocol error (Runtime.callFunctionOn): Session closed. Most likely the page has been closed.", "timestamp": 1750500663766}, {"number": "<EMAIL>", "status": "failed", "error": "Protocol error (Runtime.callFunctionOn): Session closed. Most likely the page has been closed.", "timestamp": 1750500663766}, {"number": "<EMAIL>", "status": "failed", "error": "Protocol error (Runtime.callFunctionOn): Session closed. Most likely the page has been closed.", "timestamp": 1750500663766}, {"number": "<EMAIL>", "status": "failed", "error": "Protocol error (Runtime.callFunctionOn): Session closed. Most likely the page has been closed.", "timestamp": 1750500663766}, {"number": "<EMAIL>", "status": "failed", "error": "Protocol error (Runtime.callFunctionOn): Session closed. Most likely the page has been closed.", "timestamp": 1750500663766}, {"number": "<EMAIL>", "status": "failed", "error": "Protocol error (Runtime.callFunctionOn): Session closed. Most likely the page has been closed.", "timestamp": 1750500663767}]